package com.ksher.productcontract.api.dto.response;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 销售产品信息表
 * </p>
 *
 * <AUTHOR> generator
 * @since 
 */
public class SalesProductInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 销售产品id
     */
    private String salesProductId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 销售产品码
     */
    private String salesProductCode;

    /**
     * 销售产品中文名
     */
    private String salesProductName;

    /**
     * 销售产品描述说明
     */
    private String salesProductDescription;

    /**
     * 平台产品码
     */
    private List<String> platformProductCodes;

    /**
     * 解决方案编码
     */
    private String solutionCode;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建用户
     */
    private String createUser;

    /**
     * 修改用户
     */
    private String updateUser;

    /**
     * 审核用户
     */
    private String approver;

    public String getSalesProductId() {
        return salesProductId;
    }

    public void setSalesProductId(String salesProductId) {
        this.salesProductId = salesProductId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getSalesProductCode() {
        return salesProductCode;
    }

    public void setSalesProductCode(String salesProductCode) {
        this.salesProductCode = salesProductCode;
    }

    public String getSalesProductName() {
        return salesProductName;
    }

    public void setSalesProductName(String salesProductName) {
        this.salesProductName = salesProductName;
    }

    public String getSalesProductDescription() {
        return salesProductDescription;
    }

    public void setSalesProductDescription(String salesProductDescription) {
        this.salesProductDescription = salesProductDescription;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getApprover() {
        return approver;
    }

    public void setApprover(String approver) {
        this.approver = approver;
    }

    public List<String> getPlatformProductCodes() {
        return platformProductCodes;
    }

    public void setPlatformProductCodes(List<String> platformProductCodes) {
        this.platformProductCodes = platformProductCodes;
    }

    public String getSolutionCode() {
        return solutionCode;
    }

    public void setSolutionCode(String solutionCode) {
        this.solutionCode = solutionCode;
    }

    @Override
    public String toString() {
        return "SalesProductInfoDTO{" +
        "salesProductId = " + salesProductId +
        ", createTime = " + createTime +
        ", updateTime = " + updateTime +
        ", salesProductCode = " + salesProductCode +
        ", salesProductName = " + salesProductName +
        ", salesProductDescription = " + salesProductDescription +
        ", platformProductCodes = " + platformProductCodes +
        ", solutionCode = " + solutionCode +
        ", status = " + status +
        ", createUser = " + createUser +
        ", updateUser = " + updateUser +
        ", approver = " + approver +
        "}";
    }
}
