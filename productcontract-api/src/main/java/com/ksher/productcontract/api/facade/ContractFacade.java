package com.ksher.productcontract.api.facade;

import com.ksher.framework.model.Result;
import com.ksher.productcontract.api.dto.request.BatchSignReq;
import com.ksher.productcontract.api.dto.request.ContractFileGenerateCmd;

import java.util.Map;

/**
 * <AUTHOR>
 * @Title: ContractFacade
 * @Package com.ksher.productcontract.api.facade
 * @date 2025/4/21
 */
public interface ContractFacade {

    /**
     * 签约
     *
     * @param salesProductCode 销售产品码
     * @param merchantID       商户ID
     * @return 签约结果，成功返回合约号
     */
    Result<String> signContract(String salesProductCode, String merchantID);

    /**
     * 协议文件生成
     *
     * @param contractFileGenerateCmd 生成协议文件所需的参数
     * @return 生成的协议文件的路径
     */
    Result<String> generateContractFile(ContractFileGenerateCmd contractFileGenerateCmd);

    /**
     * 批量签约
     *
     * @param batchSignReq 批量签约所需的参数, 商户ID和销售产品码
     * @return 批量签约结果，成功返回合约号
     */
    Result<Map<String, String>> batchSignContract(BatchSignReq batchSignReq);

}
