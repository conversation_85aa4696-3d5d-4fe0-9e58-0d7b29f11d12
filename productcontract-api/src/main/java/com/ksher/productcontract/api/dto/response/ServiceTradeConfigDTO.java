package com.ksher.productcontract.api.dto.response;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 服务贸易行业配置信息DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/22
 */
public class ServiceTradeConfigDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 销售产品id
     */
    private String pstConfigId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 服贸类型编码
     */
    private String serviceTradeValue;

    /**
     * 服贸类型名称
     */
    private String serviceTradeName;

    /**
     * 服贸类型英文名称
     */
    private String serviceTradeNameEn;

    /**
     * 支持的币种，逗号分割
     */
    private String currenciesSupport;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否支持结汇入境
     */
    private String fundInwardSupport;

    /**
     * 是否支持本地付
     */
    private String localPaymentSupport;

    /**
     * 是否支持全局展业
     */
    private String globalBusinessSupport;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建用户
     */
    private String createUser;

    /**
     * 修改用户
     */
    private String updateUser;

    /**
     * 审核用户
     */
    private String approver;

    public String getPstConfigId() {
        return pstConfigId;
    }

    public void setPstConfigId(String pstConfigId) {
        this.pstConfigId = pstConfigId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getServiceTradeValue() {
        return serviceTradeValue;
    }

    public void setServiceTradeValue(String serviceTradeValue) {
        this.serviceTradeValue = serviceTradeValue;
    }

    public String getServiceTradeName() {
        return serviceTradeName;
    }

    public void setServiceTradeName(String serviceTradeName) {
        this.serviceTradeName = serviceTradeName;
    }

    public String getServiceTradeNameEn() {
        return serviceTradeNameEn;
    }

    public void setServiceTradeNameEn(String serviceTradeNameEn) {
        this.serviceTradeNameEn = serviceTradeNameEn;
    }

    public String getCurrenciesSupport() {
        return currenciesSupport;
    }

    public void setCurrenciesSupport(String currenciesSupport) {
        this.currenciesSupport = currenciesSupport;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getFundInwardSupport() {
        return fundInwardSupport;
    }

    public void setFundInwardSupport(String fundInwardSupport) {
        this.fundInwardSupport = fundInwardSupport;
    }

    public String getLocalPaymentSupport() {
        return localPaymentSupport;
    }

    public void setLocalPaymentSupport(String localPaymentSupport) {
        this.localPaymentSupport = localPaymentSupport;
    }

    public String getGlobalBusinessSupport() {
        return globalBusinessSupport;
    }

    public void setGlobalBusinessSupport(String globalBusinessSupport) {
        this.globalBusinessSupport = globalBusinessSupport;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getApprover() {
        return approver;
    }

    public void setApprover(String approver) {
        this.approver = approver;
    }
}
