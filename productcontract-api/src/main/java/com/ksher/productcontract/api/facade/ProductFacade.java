package com.ksher.productcontract.api.facade;

import com.ksher.framework.model.Result;
import com.ksher.productcontract.api.dto.response.GoodsTradeCategoryDTO;
import com.ksher.productcontract.api.dto.response.PlatformProductConfigDTO;
import com.ksher.productcontract.api.dto.response.SalesProductInfoDTO;
import com.ksher.productcontract.api.dto.response.ServiceTradeConfigDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Title: ProductFacade
 * @Package com.ksher.productcontract.api.facade
 * @date 2025/4/21
 */
public interface ProductFacade {
    /**
     * 获取解决方案列表
     * @return 解决方案的 List
     */
    Result<List<SalesProductInfoDTO>> getSolutionList();

    /**
     * 根据解决方案编码获取销售产品列表
     * @param solutionCode 解决方案编码
     * @return 销售产品的 List
     */
    Result<List<SalesProductInfoDTO>> getSalesProductList(String solutionCode);

    /**
     * 根据解决方案编码获取销售产品列表
     * @param merchantID 商户id
     * @return 销售产品的 List
     */
    Result<List<SalesProductInfoDTO>> getSalesProductListByMerchant(String merchantID);

    /**
     * 根据销售产品码获取 KYB 材料列表
     * @param salesProductCode 销售产品码
     * @return 包含 KYB 材料信息的 JSON 数据的 List
     */
    Result<List<Map<String, Object>>> getKYBMaterialList(String salesProductCode);

    /**
     * 获取所有平台产品配置信息
     * @return 平台产品配置信息的 List
     */
    Result<List<PlatformProductConfigDTO>> getAllPlatformProductConfigs();


    /**
     * 获取所有货物贸易销售类目信息
     * @return 货物贸易销售类目信息的 List
     */
    Result<List<GoodsTradeCategoryDTO>> getAllGoodsTradeCategories();

    /**
     * 获取所有服务贸易行业配置信息
     * @return 服务贸易行业配置信息的 List
     */
    Result<List<ServiceTradeConfigDTO>> getAllServiceTradeConfigs();
}
