package com.ksher.productcontract.repository.impl;

import com.ksher.productcontract.converter.GoodsTradeCategoryConverter;
import com.ksher.productcontract.dao.dataobject.PcGoodsTradeCategoryDO;
import com.ksher.productcontract.dao.mapper.PcGoodsTradeCategoryMapper;
import com.ksher.productcontract.model.GoodsTradeCategory;
import com.ksher.productcontract.repository.GoodsTradeCategoryRepository;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 货物贸易销售类目仓储层实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/22
 */
@Repository
public class GoodsTradeCategoryRepositoryImpl implements GoodsTradeCategoryRepository {

    @Resource
    private PcGoodsTradeCategoryMapper pcGoodsTradeCategoryMapper;

    @Override
    public List<GoodsTradeCategory> queryAll() {
        List<PcGoodsTradeCategoryDO> pcGoodsTradeCategoryDOS = pcGoodsTradeCategoryMapper.selectAll();
        return pcGoodsTradeCategoryDOS.stream()
                .map(GoodsTradeCategoryConverter::convertToModel)
                .collect(Collectors.toList());
    }
}
