package com.ksher.productcontract.repository;

import com.ksher.productcontract.model.SalesProductInfo;

import java.math.BigDecimal;
import java.util.List;

/**
 * 销售产品信息仓储层访问接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/22
 */
public interface SalesProductInfoRepository {

    /**
     * 查询所有销售产品码相关信息
     * @return
     */
    List<SalesProductInfo> queryAll();

    /**
     * 根据解决方案编码查询销售产品码相关信息
     * @param solutionCode
     * @return
     */
    List<SalesProductInfo> queryBySolutionCode(String solutionCode);

    /**
     * 根据销售产品码列表查询销售产品信息
     *
     * @param salesProductCodes 销售产品码列表
     * @return 销售产品信息列表
     */
    List<SalesProductInfo> queryBySalesProductCodes(List<String> salesProductCodes);
}