package com.ksher.productcontract.repository.impl;

import com.ksher.productcontract.converter.PlatformProductInfoConverter;
import com.ksher.productcontract.converter.SalesProdKybInfoConverter;
import com.ksher.productcontract.converter.SalesProductInfoConverter;
import com.ksher.productcontract.converter.SolutionInfoConverter;
import com.ksher.productcontract.dao.dataobject.PcPlatformProductInfoDO;
import com.ksher.productcontract.dao.dataobject.PcSalesProductInfoDO;
import com.ksher.productcontract.dao.dataobject.PcSolutionInfoDO;
import com.ksher.productcontract.dao.mapper.*;
import com.ksher.productcontract.model.SalesProductInfo;
import com.ksher.productcontract.model.SolutionInfo;
import com.ksher.productcontract.repository.SalesProductInfoRepository;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 销售产品信息仓储层实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/22
 */
@Repository
public class SalesProductInfoRepositoryImpl implements SalesProductInfoRepository {

    @Resource
    private PcSalesProductInfoMapper pcSalesProductInfoMapper;

    @Resource
    private PcSalesPlatfmProdRelationMapper pcSalesPlatfmProdRelationMapper;

    @Resource
    private PcSolutionInfoMapper pcSolutionInfoMapper;

    @Resource
    private PcSalesProdKybInfoMapper pcSalesProdKybInfoMapper;

    @Resource
    private PcPlatformProductInfoMapper pcPlatformProductInfoMapper;

    @Override
    public List<SalesProductInfo> queryAll() {
        List<PcSalesProductInfoDO> pcSalesProductInfoDOS = pcSalesProductInfoMapper.selectAll();
        return pcSalesProductInfoDOS.stream()
                .map(SalesProductInfoConverter.INSTANCE::convertToModel)
                .collect(Collectors.toList());
    }

    @Override
    public List<SalesProductInfo> queryBySolutionCode(String solutionCode) {
        List<PcSalesProductInfoDO> pcSalesProductInfoDOS = pcSalesProductInfoMapper.selectBySolutionCode(solutionCode);

        List<SalesProductInfo> salesProductInfos = pcSalesProductInfoDOS.stream()
                .map(SalesProductInfoConverter.INSTANCE::convertToModel)
                .collect(Collectors.toList());

        // 为每个销售产品填充平台产品码列表
        for (SalesProductInfo salesProductInfo : salesProductInfos) {
            List<String> platformProductCodes = pcSalesPlatfmProdRelationMapper
                    .selectPlatformProductCodesBySalesProductCode(salesProductInfo.getSalesProductCode());
            List<PcPlatformProductInfoDO> platformProductInfos = platformProductCodes.stream().map(code -> pcPlatformProductInfoMapper.selectByPlatformProductCode(code)).collect(Collectors.toList());
            salesProductInfo.setPlatformProducts(platformProductInfos.stream()
                    .map(PlatformProductInfoConverter.INSTANCE::convertToModel)
                    .collect(Collectors.toList()));
        }

        return salesProductInfos;
    }

    @Override
    public SalesProductInfo queryBySalesProductCode(String salesProductCode) {
        if (salesProductCode == null || salesProductCode.isEmpty()) {
            return null;
        }
        PcSalesProductInfoDO pcSalesProductInfoDO = pcSalesProductInfoMapper.selectBySalesProductCode(salesProductCode);
        SalesProductInfo salesProductInfo = SalesProductInfoConverter.INSTANCE.convertToModel(pcSalesProductInfoDO);
        PcSolutionInfoDO pcSolutionInfoDO = pcSolutionInfoMapper.selectBySolutionCode(pcSalesProductInfoDO.getSolutionCode());
        SolutionInfo solutionInfo = SolutionInfoConverter.INSTANCE.convertToModel(pcSolutionInfoDO);
        salesProductInfo.setSolutionInfo(solutionInfo);

        // 为每个销售产品填充平台产品码列表
        List<String>  platformProductCodes = pcSalesPlatfmProdRelationMapper.selectPlatformProductCodesBySalesProductCode(pcSalesProductInfoDO.getSalesProductCode());
        // 根据每个平台产品码查询平台产品信息
        List<PcPlatformProductInfoDO> platformProductInfos = platformProductCodes.stream().map(code -> pcPlatformProductInfoMapper.selectByPlatformProductCode(code)).collect(Collectors.toList());

        salesProductInfo.setPlatformProducts(platformProductInfos.stream()
                .map(PlatformProductInfoConverter.INSTANCE::convertToModel)
                .collect(Collectors.toList()));
        //  为每个销售产品填充销售产品kyb信息
        salesProductInfo.setSalesProdKybInfo(SalesProdKybInfoConverter.INSTANCE.convertToModel(pcSalesProdKybInfoMapper.selectBySalesProductCode(pcSalesProductInfoDO.getSalesProductCode())).getKybInfo());

        return salesProductInfo;
    }

    @Override
    public List<SalesProductInfo> queryBySalesProductCodes(List<String> salesProductCodes) {
        if (salesProductCodes == null || salesProductCodes.isEmpty()) {
            return new ArrayList<>();
        }

        List<PcSalesProductInfoDO> pcSalesProductInfoDOS = pcSalesProductInfoMapper.selectBySalesProductCodes(salesProductCodes);
        List<SalesProductInfo> salesProductInfos = new ArrayList<>();
        // 根据销售产品码查询平台产品信息，并查询解决方案，完善销售产品信息
        for (PcSalesProductInfoDO pcSalesProductInfoDO : pcSalesProductInfoDOS) {
            SalesProductInfo salesProductInfo = SalesProductInfoConverter.INSTANCE.convertToModel(pcSalesProductInfoDO);
            PcSolutionInfoDO pcSolutionInfoDO = pcSolutionInfoMapper.selectBySolutionCode(pcSalesProductInfoDO.getSolutionCode());
            SolutionInfo solutionInfo = SolutionInfoConverter.INSTANCE.convertToModel(pcSolutionInfoDO);
            salesProductInfo.setSolutionInfo(solutionInfo);

            // 为每个销售产品填充平台产品码列表
            List<String>  platformProductCodes = pcSalesPlatfmProdRelationMapper.selectPlatformProductCodesBySalesProductCode(pcSalesProductInfoDO.getSalesProductCode());
            // 根据每个平台产品码查询平台产品信息
            List<PcPlatformProductInfoDO> platformProductInfos = platformProductCodes.stream().map(code -> pcPlatformProductInfoMapper.selectByPlatformProductCode(code)).collect(Collectors.toList());

            salesProductInfo.setPlatformProducts(platformProductInfos.stream()
                    .map(PlatformProductInfoConverter.INSTANCE::convertToModel)
                    .collect(Collectors.toList()));
            //  为每个销售产品填充销售产品kyb信息
            salesProductInfo.setSalesProdKybInfo(SalesProdKybInfoConverter.INSTANCE.convertToModel(pcSalesProdKybInfoMapper.selectBySalesProductCode(pcSalesProductInfoDO.getSalesProductCode())).getKybInfo());

            salesProductInfos.add(salesProductInfo);
        }

        return salesProductInfos;
    }
}
