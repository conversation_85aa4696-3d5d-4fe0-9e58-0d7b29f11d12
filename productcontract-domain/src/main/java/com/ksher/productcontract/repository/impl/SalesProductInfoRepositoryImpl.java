package com.ksher.productcontract.repository.impl;

import com.ksher.productcontract.converter.SalesProductInfoConverter;
import com.ksher.productcontract.dao.dataobject.PcSalesProductInfoDO;
import com.ksher.productcontract.dao.mapper.PcSalesProductInfoMapper;
import com.ksher.productcontract.model.SalesProductInfo;
import com.ksher.productcontract.repository.SalesProductInfoRepository;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 销售产品信息仓储层实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/22
 */
@Repository
public class SalesProductInfoRepositoryImpl implements SalesProductInfoRepository {

    @Resource
    private PcSalesProductInfoMapper pcSalesProductInfoMapper;

    @Override
    public List<SalesProductInfo> queryAll() {
        List<PcSalesProductInfoDO> pcSalesProductInfoDOS = pcSalesProductInfoMapper.selectAll();
        return pcSalesProductInfoDOS.stream()
                .map(SalesProductInfoConverter.INSTANCE::convertToModel)
                .collect(Collectors.toList());
    }

    @Override
    public List<SalesProductInfo> queryBySolutionCode(String solutionCode) {
        List<PcSalesProductInfoDO> pcSalesProductInfoDOS = pcSalesProductInfoMapper.selectBySolutionCode(solutionCode);
        return pcSalesProductInfoDOS.stream()
                .map(SalesProductInfoConverter.INSTANCE::convertToModel)
                .collect(Collectors.toList());
    }
}
