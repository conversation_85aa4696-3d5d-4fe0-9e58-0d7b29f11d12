package com.ksher.productcontract.repository.impl;

import com.ksher.productcontract.converter.ServiceTradeConfigConverter;
import com.ksher.productcontract.dao.dataobject.PcServiceTradeConfigDO;
import com.ksher.productcontract.dao.mapper.PcServiceTradeConfigMapper;
import com.ksher.productcontract.model.ServiceTradeConfig;
import com.ksher.productcontract.repository.ServiceTradeConfigRepository;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 服务贸易行业配置信息仓储层实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/22
 */
@Repository
public class ServiceTradeConfigRepositoryImpl implements ServiceTradeConfigRepository {

    @Resource
    private PcServiceTradeConfigMapper pcServiceTradeConfigMapper;

    @Override
    public List<ServiceTradeConfig> queryAll() {
        List<PcServiceTradeConfigDO> pcServiceTradeConfigDOS = pcServiceTradeConfigMapper.selectAll();
        return pcServiceTradeConfigDOS.stream()
                .map(ServiceTradeConfigConverter::convertToModel)
                .collect(Collectors.toList());
    }
}
