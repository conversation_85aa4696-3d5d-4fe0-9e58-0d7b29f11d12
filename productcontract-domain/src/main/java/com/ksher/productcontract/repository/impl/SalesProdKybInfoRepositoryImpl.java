package com.ksher.productcontract.repository.impl;

import com.ksher.productcontract.converter.SalesProdKybInfoConverter;
import com.ksher.productcontract.dao.dataobject.PcSalesProdKybInfoDO;
import com.ksher.productcontract.dao.mapper.PcSalesProdKybInfoMapper;
import com.ksher.productcontract.model.SalesProdKybInfo;
import com.ksher.productcontract.repository.SalesProdKybInfoRepository;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 销售产品与KYB信息仓储层实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/22
 */
@Repository
public class SalesProdKybInfoRepositoryImpl implements SalesProdKybInfoRepository {

    @Resource
    private PcSalesProdKybInfoMapper pcSalesProdKybInfoMapper;

    @Override
    public SalesProdKybInfo queryBySalesProductCode(String salesProductCode) {
        PcSalesProdKybInfoDO pcSalesProdKybInfoDO = pcSalesProdKybInfoMapper.selectBySalesProductCode(salesProductCode);
        return SalesProdKybInfoConverter.INSTANCE.convertToModel(pcSalesProdKybInfoDO);
    }
}
