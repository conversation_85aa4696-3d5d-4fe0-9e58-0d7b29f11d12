package com.ksher.productcontract.converter;

import com.ksher.productcontract.api.dto.response.GoodsTradeCategoryDTO;
import com.ksher.productcontract.dao.dataobject.PcGoodsTradeCategoryDO;
import com.ksher.productcontract.model.GoodsTradeCategory;

/**
 * 货物贸易销售类目转换器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/22
 */
public class GoodsTradeCategoryConverter {

    /**
     * 将数据对象转换为领域模型
     *
     * @param pcGoodsTradeCategoryDO 货物贸易销售类目数据对象
     * @return 货物贸易销售类目领域模型
     */
    public static GoodsTradeCategory convertToModel(PcGoodsTradeCategoryDO pcGoodsTradeCategoryDO) {
        if (null == pcGoodsTradeCategoryDO) {
            return null;
        }
        GoodsTradeCategory goodsTradeCategory = new GoodsTradeCategory();
        goodsTradeCategory.setPgtCatagoryId(pcGoodsTradeCategoryDO.getPgtCatagoryId());
        goodsTradeCategory.setCreateTime(pcGoodsTradeCategoryDO.getCreateTime());
        goodsTradeCategory.setUpdateTime(pcGoodsTradeCategoryDO.getUpdateTime());
        goodsTradeCategory.setCatagoryValue(pcGoodsTradeCategoryDO.getCatagoryValue());
        goodsTradeCategory.setCatagoryName(pcGoodsTradeCategoryDO.getCatagoryName());
        goodsTradeCategory.setCatagoryNameEn(pcGoodsTradeCategoryDO.getCatagoryNameEn());
        goodsTradeCategory.setStatus(pcGoodsTradeCategoryDO.getStatus());
        goodsTradeCategory.setCreateUser(pcGoodsTradeCategoryDO.getCreateUser());
        goodsTradeCategory.setUpdateUser(pcGoodsTradeCategoryDO.getUpdateUser());
        goodsTradeCategory.setApprover(pcGoodsTradeCategoryDO.getApprover());
        return goodsTradeCategory;
    }

    /**
     * 将领域模型转换为数据对象
     *
     * @param goodsTradeCategory 货物贸易销售类目领域模型
     * @return 货物贸易销售类目数据对象
     */
    public static PcGoodsTradeCategoryDO convertToDO(GoodsTradeCategory goodsTradeCategory) {
        if (null == goodsTradeCategory) {
            return null;
        }
        PcGoodsTradeCategoryDO pcGoodsTradeCategoryDO = new PcGoodsTradeCategoryDO();
        pcGoodsTradeCategoryDO.setPgtCatagoryId(goodsTradeCategory.getPgtCatagoryId());
        pcGoodsTradeCategoryDO.setCreateTime(goodsTradeCategory.getCreateTime());
        pcGoodsTradeCategoryDO.setUpdateTime(goodsTradeCategory.getUpdateTime());
        pcGoodsTradeCategoryDO.setCatagoryValue(goodsTradeCategory.getCatagoryValue());
        pcGoodsTradeCategoryDO.setCatagoryName(goodsTradeCategory.getCatagoryName());
        pcGoodsTradeCategoryDO.setCatagoryNameEn(goodsTradeCategory.getCatagoryNameEn());
        pcGoodsTradeCategoryDO.setStatus(goodsTradeCategory.getStatus());
        pcGoodsTradeCategoryDO.setCreateUser(goodsTradeCategory.getCreateUser());
        pcGoodsTradeCategoryDO.setUpdateUser(goodsTradeCategory.getUpdateUser());
        pcGoodsTradeCategoryDO.setApprover(goodsTradeCategory.getApprover());
        return pcGoodsTradeCategoryDO;
    }

    /**
     * 将领域模型转换为DTO
     *
     * @param goodsTradeCategory 货物贸易销售类目领域模型
     * @return 货物贸易销售类目DTO
     */
    public static GoodsTradeCategoryDTO convertToDTO(GoodsTradeCategory goodsTradeCategory) {
        if (null == goodsTradeCategory) {
            return null;
        }
        GoodsTradeCategoryDTO goodsTradeCategoryDTO = new GoodsTradeCategoryDTO();
        goodsTradeCategoryDTO.setPgtCatagoryId(goodsTradeCategory.getPgtCatagoryId());
        goodsTradeCategoryDTO.setCreateTime(goodsTradeCategory.getCreateTime());
        goodsTradeCategoryDTO.setUpdateTime(goodsTradeCategory.getUpdateTime());
        goodsTradeCategoryDTO.setCatagoryValue(goodsTradeCategory.getCatagoryValue());
        goodsTradeCategoryDTO.setCatagoryName(goodsTradeCategory.getCatagoryName());
        goodsTradeCategoryDTO.setCatagoryNameEn(goodsTradeCategory.getCatagoryNameEn());
        goodsTradeCategoryDTO.setStatus(goodsTradeCategory.getStatus());
        goodsTradeCategoryDTO.setCreateUser(goodsTradeCategory.getCreateUser());
        goodsTradeCategoryDTO.setUpdateUser(goodsTradeCategory.getUpdateUser());
        goodsTradeCategoryDTO.setApprover(goodsTradeCategory.getApprover());
        return goodsTradeCategoryDTO;
    }
}
