package com.ksher.productcontract.converter;

import com.ksher.productcontract.dao.dataobject.PcServiceTradeConfigDO;
import com.ksher.productcontract.model.ServiceTradeConfig;

/**
 * 服务贸易行业配置信息转换器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/22
 */
public class ServiceTradeConfigConverter {

    /**
     * 将数据对象转换为领域模型
     *
     * @param pcServiceTradeConfigDO 服务贸易行业配置信息数据对象
     * @return 服务贸易行业配置信息领域模型
     */
    public static ServiceTradeConfig convertToModel(PcServiceTradeConfigDO pcServiceTradeConfigDO) {
        if (null == pcServiceTradeConfigDO) {
            return null;
        }
        ServiceTradeConfig serviceTradeConfig = new ServiceTradeConfig();
        serviceTradeConfig.setPstConfigId(pcServiceTradeConfigDO.getPstConfigId());
        serviceTradeConfig.setCreateTime(pcServiceTradeConfigDO.getCreateTime());
        serviceTradeConfig.setUpdateTime(pcServiceTradeConfigDO.getUpdateTime());
        serviceTradeConfig.setServiceTradeValue(pcServiceTradeConfigDO.getServiceTradeValue());
        serviceTradeConfig.setServiceTradeName(pcServiceTradeConfigDO.getServiceTradeName());
        serviceTradeConfig.setServiceTradeNameEn(pcServiceTradeConfigDO.getServiceTradeNameEn());
        serviceTradeConfig.setCurrenciesSupport(pcServiceTradeConfigDO.getCurrenciesSupport());
        serviceTradeConfig.setDescription(pcServiceTradeConfigDO.getDescription());
        serviceTradeConfig.setFundInwardSupport(pcServiceTradeConfigDO.getFundInwardSupport());
        serviceTradeConfig.setLocalPaymentSupport(pcServiceTradeConfigDO.getLocalPaymentSupport());
        serviceTradeConfig.setGlobalBusinessSupport(pcServiceTradeConfigDO.getGlobalBusinessSupport());
        serviceTradeConfig.setStatus(pcServiceTradeConfigDO.getStatus());
        serviceTradeConfig.setCreateUser(pcServiceTradeConfigDO.getCreateUser());
        serviceTradeConfig.setUpdateUser(pcServiceTradeConfigDO.getUpdateUser());
        serviceTradeConfig.setApprover(pcServiceTradeConfigDO.getApprover());
        return serviceTradeConfig;
    }

    /**
     * 将领域模型转换为数据对象
     *
     * @param serviceTradeConfig 服务贸易行业配置信息领域模型
     * @return 服务贸易行业配置信息数据对象
     */
    public static PcServiceTradeConfigDO convertToDO(ServiceTradeConfig serviceTradeConfig) {
        if (null == serviceTradeConfig) {
            return null;
        }
        PcServiceTradeConfigDO pcServiceTradeConfigDO = new PcServiceTradeConfigDO();
        pcServiceTradeConfigDO.setPstConfigId(serviceTradeConfig.getPstConfigId());
        pcServiceTradeConfigDO.setCreateTime(serviceTradeConfig.getCreateTime());
        pcServiceTradeConfigDO.setUpdateTime(serviceTradeConfig.getUpdateTime());
        pcServiceTradeConfigDO.setServiceTradeValue(serviceTradeConfig.getServiceTradeValue());
        pcServiceTradeConfigDO.setServiceTradeName(serviceTradeConfig.getServiceTradeName());
        pcServiceTradeConfigDO.setServiceTradeNameEn(serviceTradeConfig.getServiceTradeNameEn());
        pcServiceTradeConfigDO.setCurrenciesSupport(serviceTradeConfig.getCurrenciesSupport());
        pcServiceTradeConfigDO.setDescription(serviceTradeConfig.getDescription());
        pcServiceTradeConfigDO.setFundInwardSupport(serviceTradeConfig.getFundInwardSupport());
        pcServiceTradeConfigDO.setLocalPaymentSupport(serviceTradeConfig.getLocalPaymentSupport());
        pcServiceTradeConfigDO.setGlobalBusinessSupport(serviceTradeConfig.getGlobalBusinessSupport());
        pcServiceTradeConfigDO.setStatus(serviceTradeConfig.getStatus());
        pcServiceTradeConfigDO.setCreateUser(serviceTradeConfig.getCreateUser());
        pcServiceTradeConfigDO.setUpdateUser(serviceTradeConfig.getUpdateUser());
        pcServiceTradeConfigDO.setApprover(serviceTradeConfig.getApprover());
        return pcServiceTradeConfigDO;
    }
}
