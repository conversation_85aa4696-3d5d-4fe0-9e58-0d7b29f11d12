package com.ksher.productcontract.coreservice;

import com.ksher.productcontract.model.GoodsTradeCategory;
import com.ksher.productcontract.model.PlatformProductConfig;
import com.ksher.productcontract.model.SalesProdKybInfo;
import com.ksher.productcontract.model.SalesProductInfo;
import com.ksher.productcontract.model.ServiceTradeConfig;
import com.ksher.productcontract.model.SolutionInfo;

import java.util.List;
import java.util.Map;

/**
 * 产品核心服务接口
 *
 * <AUTHOR>
 * @Title: ProductCoreService
 * @Package com.ksher.productcontract.coreservice
 * @date 2025/5/16
 */
public interface ProductCoreService {

    /**
     * 查询所有业务解决方案列表
     *
     * @return 所有解决方案信息列表
     */
    List<SolutionInfo> querySolutionList();

    /**
     * 根据解决方案编码查询销售产品列表
     *
     * @param solutionCode 解决方案编码
     * @return 销售产品信息列表
     */
    List<SalesProductInfo> querySalesProductList(String solutionCode);

    /**
     * 根据销售产品编码查询KYB材料列表
     *
     * @param salesProductCode 销售产品编码
     * @return KYB材料信息列表
     */
    List<SalesProdKybInfo> queryKybMaterialList(String salesProductCode);

    /**
     * 查询所有平台产品配置信息
     *
     * @return 平台产品配置信息列表
     */
    List<PlatformProductConfig> queryAllPlatformProductConfigs();

    /**
     * 查询所有货物贸易销售类目信息
     *
     * @return 货物贸易销售类目信息列表
     */
    List<GoodsTradeCategory> queryAllGoodsTradeCategories();

    /**
     * 查询所有服务贸易行业配置信息
     *
     * @return 服务贸易行业配置信息列表
     */
    List<ServiceTradeConfig> queryAllServiceTradeConfigs();

    /**
     * 根据商户ID查询商户签约的销售产品信息
     *
     * @param merchantId 商户ID
     * @return 销售产品信息列表
     */
    List<SalesProductInfo> querySalesProductListByMerchant(String merchantId);
}
