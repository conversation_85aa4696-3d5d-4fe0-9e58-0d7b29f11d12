package com.ksher.productcontract.coreservice.impl;

import com.ksher.productcontract.coreservice.ProductCoreService;
import com.ksher.productcontract.model.GoodsTradeCategory;
import com.ksher.productcontract.model.PlatformProductConfig;
import com.ksher.productcontract.model.SalesProdKybInfo;
import com.ksher.productcontract.model.SalesProductInfo;
import com.ksher.productcontract.model.ServiceTradeConfig;
import com.ksher.productcontract.model.SolutionInfo;
import com.ksher.productcontract.repository.GoodsTradeCategoryRepository;
import com.ksher.productcontract.repository.PlatformProductConfigRepository;
import com.ksher.productcontract.repository.SalesProdKybInfoRepository;
import com.ksher.productcontract.repository.SalesProductInfoRepository;
import com.ksher.productcontract.repository.ServiceTradeConfigRepository;
import com.ksher.productcontract.repository.SolutionInfoRepository;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 产品核心服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/22
 */
@Service
public class ProductCoreServiceImpl implements ProductCoreService {

    @Resource
    private SolutionInfoRepository solutionInfoRepository;

    @Resource
    private SalesProductInfoRepository salesProductInfoRepository;

    @Resource
    private SalesProdKybInfoRepository salesProdKybInfoRepository;

    @Resource
    private PlatformProductConfigRepository platformProductConfigRepository;

    @Override
    public List<SolutionInfo> querySolutionList() {
        return solutionInfoRepository.queryAll();
    }

    @Override
    public List<SalesProductInfo> querySalesProductList(String solutionCode) {
        return salesProductInfoRepository.queryBySolutionCode(solutionCode);
    }

    /**
     * @param salesProductCode 销售产品编码
     * @return
     */
    @Override
    public List<SalesProdKybInfo> queryKybMaterialList(String salesProductCode) {
        // 这里需要先根据salesProductCode获取salesProductId，然后再查询KYB材料
        // 由于我们没有直接通过salesProductCode查询的方法，这里简化处理
        // 实际实现可能需要先查询SalesProductInfo，然后再根据ID查询KYB材料

        // 假设我们有一个方法可以根据salesProductCode获取salesProductId

            return salesProdKybInfoRepository.queryBySalesProductCode(salesProductCode);
    }

    @Override
    public List<PlatformProductConfig> queryAllPlatformProductConfigs() {
        return platformProductConfigRepository.queryAll();
    }
}
