package com.ksher.productcontract.coreservice.impl;

import com.ksher.productcontract.coreservice.ProductCoreService;
import com.ksher.productcontract.dao.mapper.PcSalesPlatfmProdRelationMapper;
import com.ksher.productcontract.model.GoodsTradeCategory;
import com.ksher.productcontract.model.PlatformProductConfig;
import com.ksher.productcontract.model.SalesProdKybInfo;
import com.ksher.productcontract.model.SalesProductInfo;
import com.ksher.productcontract.model.ServiceTradeConfig;
import com.ksher.productcontract.model.SolutionInfo;
import com.ksher.productcontract.repository.GoodsTradeCategoryRepository;
import com.ksher.productcontract.repository.PlatformProductConfigRepository;
import com.ksher.productcontract.repository.SalesProdKybInfoRepository;
import com.ksher.productcontract.repository.SalesProductInfoRepository;
import com.ksher.productcontract.repository.ServiceTradeConfigRepository;
import com.ksher.productcontract.repository.SolutionInfoRepository;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 产品核心服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/22
 */
@Service
public class ProductCoreServiceImpl implements ProductCoreService {

    @Resource
    private SolutionInfoRepository solutionInfoRepository;

    @Resource
    private SalesProductInfoRepository salesProductInfoRepository;

    @Resource
    private SalesProdKybInfoRepository salesProdKybInfoRepository;

    @Resource
    private PlatformProductConfigRepository platformProductConfigRepository;

    @Resource
    private GoodsTradeCategoryRepository goodsTradeCategoryRepository;

    @Resource
    private ServiceTradeConfigRepository serviceTradeConfigRepository;

    @Resource
    private PcSalesPlatfmProdRelationMapper pcSalesPlatfmProdRelationMapper;

    @Override
    public List<SolutionInfo> querySolutionList() {
        return solutionInfoRepository.queryAll();
    }

    @Override
    public List<SalesProductInfo> querySalesProductList(String solutionCode) {
        List<SalesProductInfo> salesProductInfos = salesProductInfoRepository.queryBySolutionCode(solutionCode);

        // 为每个销售产品填充平台产品码列表
        for (SalesProductInfo salesProductInfo : salesProductInfos) {
            List<String> platformProductCodes = pcSalesPlatfmProdRelationMapper
                    .selectPlatformProductCodesBySalesProductCode(salesProductInfo.getSalesProductCode());
            salesProductInfo.setPlatformProductCodes(platformProductCodes);
        }

        return salesProductInfos;
    }

    /**
     * @param salesProductCode 销售产品编码
     * @return
     */
    @Override
    public List<SalesProdKybInfo> queryKybMaterialList(String salesProductCode) {
        return salesProdKybInfoRepository.queryBySalesProductCode(salesProductCode);
    }

    @Override
    public List<PlatformProductConfig> queryAllPlatformProductConfigs() {
        return platformProductConfigRepository.queryAll();
    }

    @Override
    public List<GoodsTradeCategory> queryAllGoodsTradeCategories() {
        return goodsTradeCategoryRepository.queryAll();
    }

    @Override
    public List<ServiceTradeConfig> queryAllServiceTradeConfigs() {
        return serviceTradeConfigRepository.queryAll();
    }
}
