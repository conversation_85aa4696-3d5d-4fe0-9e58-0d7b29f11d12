<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ksher.productcontract.dao.mapper.PcGoodsTradeCategoryMapper">

    <!-- 查询所有货物贸易销售类目信息 -->
    <select id="selectAll" resultMap="com.ksher.productcontract.dao.mapper.base.PcGoodsTradeCategoryBaseMapper.BaseResultMap">
        select
        <include refid="com.ksher.productcontract.dao.mapper.base.PcGoodsTradeCategoryBaseMapper.Base_Column_List"/>
        from
        pc_goods_trade_category
        order by create_time desc
    </select>

</mapper>
