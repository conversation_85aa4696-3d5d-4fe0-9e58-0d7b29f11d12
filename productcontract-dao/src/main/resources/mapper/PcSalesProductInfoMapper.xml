<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ksher.productcontract.dao.mapper.PcSalesProductInfoMapper">

    <!-- 查询所有销售产品信息 -->
    <select id="selectAll" resultMap="com.ksher.productcontract.dao.mapper.base.PcSalesProductInfoBaseMapper.BaseResultMap">
        select
        <include refid="com.ksher.productcontract.dao.mapper.base.PcSalesProductInfoBaseMapper.Base_Column_List"/>
        from
        pc_sales_product_info
        order by create_time desc
    </select>

    <!-- 根据解决方案编码查询销售产品信息 -->
    <select id="selectBySolutionCode" parameterType="String" resultMap="com.ksher.productcontract.dao.mapper.base.PcSalesProductInfoBaseMapper.BaseResultMap">
        select
        spi.*
        from
        pc_sales_product_info spi
        join pc_solution_sales_prod_relation sspr on spi.sales_product_code = sspr.sales_product_code
        join pc_solution_info si on sspr.solution_code = si.solution_code
        where
        si.solution_code = #{solutionCode,jdbcType=VARCHAR}
        order by spi.create_time desc
    </select>

    <!-- 根据销售产品码列表查询销售产品信息 -->
    <select id="selectBySalesProductCodes" parameterType="java.util.List" resultMap="com.ksher.productcontract.dao.mapper.base.PcSalesProductInfoBaseMapper.BaseResultMap">
        select
        <include refid="com.ksher.productcontract.dao.mapper.base.PcSalesProductInfoBaseMapper.Base_Column_List"/>
        from
        pc_sales_product_info
        where
        sales_product_code in
        <foreach collection="list" item="salesProductCode" open="(" separator="," close=")">
            #{salesProductCode,jdbcType=VARCHAR}
        </foreach>
        order by create_time desc
    </select>

</mapper>
