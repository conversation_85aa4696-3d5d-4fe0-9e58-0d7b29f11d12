<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ksher.productcontract.dao.mapper.PcMerchantContractMapper">

    <!-- 根据商户ID查询商户签约的销售产品码列表 -->
    <select id="selectSalesProductCodesByMerchantId" parameterType="String" resultType="String">
        select distinct sales_product_code
        from pc_merchant_contract
        where merchant_id = #{merchantId,jdbcType=VARCHAR}
        order by sales_product_code
    </select>

</mapper>
