<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ksher.productcontract.dao.mapper.PcSalesPlatfmProdRelationMapper">

    <!-- 根据销售产品码查询平台产品码列表 -->
    <select id="selectPlatformProductCodesBySalesProductCode" parameterType="String" resultType="String">
        select platform_product_code
        from pc_sales_platfm_prod_relation
        where sales_product_code = #{salesProductCode,jdbcType=VARCHAR}
        order by create_time desc
    </select>

</mapper>
