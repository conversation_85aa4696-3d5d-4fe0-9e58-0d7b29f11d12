package com.ksher.productcontract.dao.mapper;

import com.ksher.productcontract.dao.dataobject.PcGoodsTradeCategoryDO;
import com.ksher.productcontract.dao.mapper.base.PcGoodsTradeCategoryBaseMapper;

import java.util.List;

/**
 * <p>
 * 货物贸易销售类目表 Mapper 接口
 * </p>
 *
 * <AUTHOR> href="#">Alex</a>
 * @since PcGoodsTradeCategoryMapper.java v1.0 2025年06月06日 16:06 Alex Exp $
 */
public interface PcGoodsTradeCategoryMapper extends PcGoodsTradeCategoryBaseMapper {

    /**
     * 查询所有货物贸易销售类目信息
     *
     * @return 货物贸易销售类目信息列表
     */
    List<PcGoodsTradeCategoryDO> selectAll();

}
