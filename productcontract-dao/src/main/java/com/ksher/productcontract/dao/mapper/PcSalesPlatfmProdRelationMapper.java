package com.ksher.productcontract.dao.mapper;

import com.ksher.productcontract.dao.dataobject.PcSalesPlatfmProdRelationDO;
import com.ksher.productcontract.dao.mapper.base.PcSalesPlatfmProdRelationBaseMapper;

import java.util.List;

/**
 * <p>
 * 销售产品与平台产品关联关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR> href="#">Alex</a>
 * @since PcSalesPlatfmProdRelationMapper.java v1.0 2025年04月21日 11:22 Alex Exp $
 */
public interface PcSalesPlatfmProdRelationMapper extends PcSalesPlatfmProdRelationBaseMapper {

    /**
     * 根据销售产品码查询平台产品码列表
     *
     * @param salesProductCode 销售产品码
     * @return 平台产品码列表
     */
    List<String> selectPlatformProductCodesBySalesProductCode(String salesProductCode);

}
