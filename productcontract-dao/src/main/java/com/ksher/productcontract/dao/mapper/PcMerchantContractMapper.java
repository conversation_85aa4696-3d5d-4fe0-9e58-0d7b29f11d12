package com.ksher.productcontract.dao.mapper;

import com.ksher.productcontract.dao.dataobject.PcMerchantContractDO;
import com.ksher.productcontract.dao.mapper.base.PcMerchantContractBaseMapper;

import java.util.List;

/**
 * <p>
 * 商户签约协议表 Mapper 接口
 * </p>
 *
 * <AUTHOR> href="#">Alex</a>
 * @since PcMerchantContractMapper.java v1.0 2025年04月21日 11:22 Alex Exp $
 */
public interface PcMerchantContractMapper extends PcMerchantContractBaseMapper {

    /**
     * 根据商户ID查询商户签约的销售产品码列表
     *
     * @param merchantId 商户ID
     * @return 销售产品码列表
     */
    List<String> selectSalesProductCodesByMerchantId(String merchantId);

}
