package com.ksher.productcontract.dao.mapper;

import com.ksher.productcontract.dao.dataobject.PcServiceTradeConfigDO;
import com.ksher.productcontract.dao.mapper.base.PcServiceTradeConfigBaseMapper;

import java.util.List;

/**
 * <p>
 * 服务贸易行业配置信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR> href="#">Alex</a>
 * @since PcServiceTradeConfigMapper.java v1.0 2025年06月06日 15:57 Alex Exp $
 */
public interface PcServiceTradeConfigMapper extends PcServiceTradeConfigBaseMapper {

    /**
     * 查询所有服务贸易行业配置信息
     *
     * @return 服务贸易行业配置信息列表
     */
    List<PcServiceTradeConfigDO> selectAll();

}
