package com.ksher.productcontract.dao.mapper;

import com.ksher.productcontract.dao.dataobject.PcSalesProductInfoDO;
import com.ksher.productcontract.dao.mapper.base.PcSalesProductInfoBaseMapper;

import java.util.List;

/**
 * <p>
 * 销售产品信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR> href="#">Alex</a>
 * @since PcSalesProductInfoMapper.java v1.0 2025年04月21日 11:22 Alex Exp $
 */
public interface PcSalesProductInfoMapper extends PcSalesProductInfoBaseMapper {

    /**
     * 查询所有销售产品信息
     *
     * @return 销售产品信息列表
     */
    List<PcSalesProductInfoDO> selectAll();

    /**
     * 根据解决方案编码查询销售产品信息
     *
     * @param solutionCode 解决方案编码
     * @return 销售产品信息列表
     */
    List<PcSalesProductInfoDO> selectBySolutionCode(String solutionCode);

    /**
     * 根据销售产品码列表查询销售产品信息
     *
     * @param salesProductCodes 销售产品码列表
     * @return 销售产品信息列表
     */
    List<PcSalesProductInfoDO> selectBySalesProductCodes(List<String> salesProductCodes);

}
