package com.ksher.productcontract.dao.mapper;

import com.ksher.productcontract.dao.dataobject.PcSalesProdKybInfoDO;
import com.ksher.productcontract.dao.mapper.base.PcSalesProdKybInfoBaseMapper;

/**
 * <p>
 * 销售产品KYB信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR> href="#">Alex</a>
 * @since PcSalesProdKybInfoMapper.java v1.0 2025年04月21日 11:22 Alex Exp $
 */
public interface PcSalesProdKybInfoMapper extends PcSalesProdKybInfoBaseMapper {

    /**
     * 根据销售产品ID查询KYB信息
     *
     * @param salesProductCode 销售产品code
     * @return KYB信息
     */
    PcSalesProdKybInfoDO selectBySalesProductCode(String salesProductCode);

}
