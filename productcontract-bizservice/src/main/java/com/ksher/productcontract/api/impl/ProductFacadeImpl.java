package com.ksher.productcontract.api.impl;

import com.alibaba.excel.util.StringUtils;
import com.ksher.framework.model.Result;
import com.ksher.productcontract.api.dto.response.GoodsTradeCategoryDTO;
import com.ksher.productcontract.api.dto.response.PlatformProductConfigDTO;
import com.ksher.productcontract.api.dto.response.SalesProductInfoDTO;
import com.ksher.productcontract.api.dto.response.ServiceTradeConfigDTO;
import com.ksher.productcontract.api.facade.ProductFacade;
import com.ksher.productcontract.common.exception.ProductContractErrorEnum;
import com.ksher.productcontract.converter.GoodsTradeCategoryConverter;
import com.ksher.productcontract.converter.PlatformProductConfigConverter;
import com.ksher.productcontract.converter.SalesProductInfoConverter;
import com.ksher.productcontract.converter.ServiceTradeConfigConverter;
import com.ksher.productcontract.coreservice.ProductCoreService;
import com.ksher.productcontract.model.GoodsTradeCategory;
import com.ksher.productcontract.model.PlatformProductConfig;
import com.ksher.productcontract.model.SalesProdKybInfo;
import com.ksher.productcontract.model.SalesProductInfo;
import com.ksher.productcontract.model.ServiceTradeConfig;
import com.ksher.productcontract.model.SolutionInfo;
import com.ksher.productcontract.validate.ParamValidate;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 产品服务接口实现
 *
 * <AUTHOR>
 * @Title: ProductFacadeImpl
 * @Package com.ksher.productcontract.api.impl
 * @date 2025/5/16
 */
@DubboService(version = "1.0.0", timeout = 3000, interfaceClass = ProductFacade.class)
public class ProductFacadeImpl implements ProductFacade {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductFacadeImpl.class);

    @Resource
    private ProductCoreService productCoreService;

    @Resource
    private SalesProductInfoConverter salesProductInfoConverter;

    @Resource
    private PlatformProductConfigConverter platformProductConfigConverter;

    @Override
    public Result<List<SalesProductInfoDTO>> getSolutionList() {
        try {
            LOGGER.info("开始查询解决方案列表");

            // 调用核心服务查询解决方案列表
            List<SolutionInfo> solutionInfoList = productCoreService.querySolutionList();

            // 将解决方案信息转换为销售产品信息DTO
            // 注意：这里是一个适配，实际上应该返回SolutionInfoDTO，但接口定义要求返回SalesProductInfoDTO
            List<SalesProductInfoDTO> salesProductInfoDTOList = new ArrayList<>();
            for (SolutionInfo solutionInfo : solutionInfoList) {
                SalesProductInfoDTO dto = getSalesProductInfoDTO(solutionInfo);
                salesProductInfoDTOList.add(dto);
            }

            LOGGER.info("查询解决方案列表成功，数量：{}", salesProductInfoDTOList.size());
            return Result.success(salesProductInfoDTOList);
        } catch (Exception e) {
            LOGGER.error("查询解决方案列表失败", e);
            return Result.error(ProductContractErrorEnum.GET_SOLUTION_ERROR.getCode(), "查询解决方案列表失败：" + e.getMessage());
        }
    }

    private static SalesProductInfoDTO getSalesProductInfoDTO(SolutionInfo solutionInfo) {
        SalesProductInfoDTO dto = new SalesProductInfoDTO();
        dto.setSalesProductId(solutionInfo.getSolutionId());
        dto.setSalesProductCode(solutionInfo.getSolutionCode());
        dto.setSalesProductName(solutionInfo.getSolutionName());
        dto.setSalesProductDescription(solutionInfo.getSolutionDescription());
        dto.setStatus(solutionInfo.getStatus());
        dto.setCreateTime(solutionInfo.getCreateTime());
        dto.setUpdateTime(solutionInfo.getUpdateTime());
        dto.setCreateUser(solutionInfo.getCreateUser());
        dto.setUpdateUser(solutionInfo.getUpdateUser());
        dto.setApprover(solutionInfo.getApprover());
        return dto;
    }

    @Override
    public Result<List<SalesProductInfoDTO>> getSalesProductList(String solutionCode) {
        try {
            LOGGER.info("开始查询销售产品列表，解决方案编码：{}", solutionCode);

            // 参数校验
            ParamValidate.checkArgument(StringUtils.isEmpty(solutionCode), "解决方案编码不能为空");

            // 调用核心服务查询销售产品列表
            List<SalesProductInfo> salesProductInfoList = productCoreService.querySalesProductList(solutionCode);

            // 将领域模型转换为DTO
            List<SalesProductInfoDTO> salesProductInfoDTOList = salesProductInfoList.stream()
                    .map(salesProductInfoConverter::convertToDTO)
                    .collect(Collectors.toList());

            LOGGER.info("查询销售产品列表成功，数量：{}", salesProductInfoDTOList.size());
            return Result.success(salesProductInfoDTOList);
        } catch (Exception e) {
            LOGGER.error("查询销售产品列表失败", e);
            return Result.error(ProductContractErrorEnum.GET_SALES_PRODUCT_ERROR.getCode(), "查询销售产品列表失败" + e.getMessage());
        }
    }

    @Override
    public Result<List<Map<String, Object>>> getKYBMaterialList(String salesProductCode) {
        try {
            LOGGER.info("开始查询KYB材料列表，销售产品码：{}", salesProductCode);

            // 参数校验
            ParamValidate.checkArgument(StringUtils.isEmpty(salesProductCode), "销售产品码不能为空");

            // 调用核心服务查询KYB材料列表
            List<SalesProdKybInfo> kybInfoList = productCoreService.queryKybMaterialList(salesProductCode);

            // 将领域模型转换为Map
            List<Map<String, Object>> resultList = new ArrayList<>();
            for (SalesProdKybInfo kybInfo : kybInfoList) {
                Map<String, Object> map = new HashMap<>();
                map.put("pspkiId", kybInfo.getPspkiId());
                map.put("salesProductCode", kybInfo.getSalesProductCode());
                map.put("kybFieldName", kybInfo.getExtInfo().get("kybFieldName"));
                map.put("kybFieldType", kybInfo.getExtInfo().get("kybFieldType"));
                map.put("kybFieldDesc", kybInfo.getExtInfo().get("kybFieldDesc"));
                map.put("kybFieldRequired", kybInfo.getExtInfo().get("kybFieldRequired"));
                resultList.add(map);
            }

            LOGGER.info("查询KYB材料列表成功，数量：{}", resultList.size());
            return Result.success(resultList);
        } catch (Exception e) {
            LOGGER.error("查询KYB材料列表失败", e);
            return Result.error(ProductContractErrorEnum.GET_SALES_PRODUCT_KYB_ERROR.getCode(), "查询KYB材料列表失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<PlatformProductConfigDTO>> getAllPlatformProductConfigs() {
        try {
            LOGGER.info("开始查询所有平台产品配置信息");

            // 调用核心服务查询所有平台产品配置信息
            List<PlatformProductConfig> platformProductConfigs = productCoreService.queryAllPlatformProductConfigs();

            // 将领域模型转换为DTO
            List<PlatformProductConfigDTO> platformProductConfigDTOs = platformProductConfigs.stream()
                    .map(platformProductConfigConverter::convertToDTO)
                    .collect(Collectors.toList());

            LOGGER.info("查询所有平台产品配置信息成功，数量：{}", platformProductConfigDTOs.size());
            return Result.success(platformProductConfigDTOs);
        } catch (Exception e) {
            LOGGER.error("查询所有平台产品配置信息失败", e);
            return Result.error(ProductContractErrorEnum.GET_PLATFORM_PRODUCT_ERROR.getCode(), "查询所有平台产品配置信息失败" + e.getMessage());
        }
    }

    @Override
    public Result<List<GoodsTradeCategoryDTO>> getAllGoodsTradeCategories() {
        try {
            LOGGER.info("开始查询所有货物贸易销售类目信息");

            // 调用核心服务查询所有货物贸易销售类目信息
            List<GoodsTradeCategory> goodsTradeCategories = productCoreService.queryAllGoodsTradeCategories();

            // 将领域模型转换为DTO
            List<GoodsTradeCategoryDTO> goodsTradeCategoryDTOs = goodsTradeCategories.stream()
                    .map(GoodsTradeCategoryConverter::convertToDTO)
                    .collect(Collectors.toList());

            LOGGER.info("查询所有货物贸易销售类目信息成功，数量：{}", goodsTradeCategoryDTOs.size());
            return Result.success(goodsTradeCategoryDTOs);
        } catch (Exception e) {
            LOGGER.error("查询所有货物贸易销售类目信息失败", e);
            return Result.error(ProductContractErrorEnum.GET_PLATFORM_PRODUCT_ERROR.getCode(), "查询所有货物贸易销售类目信息失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<ServiceTradeConfigDTO>> getAllServiceTradeConfigs() {
        try {
            LOGGER.info("开始查询所有服务贸易行业配置信息");

            // 调用核心服务查询所有服务贸易行业配置信息
            List<ServiceTradeConfig> serviceTradeConfigs = productCoreService.queryAllServiceTradeConfigs();

            // 将领域模型转换为DTO
            List<ServiceTradeConfigDTO> serviceTradeConfigDTOs = serviceTradeConfigs.stream()
                    .map(ServiceTradeConfigConverter::convertToDTO)
                    .collect(Collectors.toList());

            LOGGER.info("查询所有服务贸易行业配置信息成功，数量：{}", serviceTradeConfigDTOs.size());
            return Result.success(serviceTradeConfigDTOs);
        } catch (Exception e) {
            LOGGER.error("查询所有服务贸易行业配置信息失败", e);
            return Result.error(ProductContractErrorEnum.GET_PLATFORM_PRODUCT_ERROR.getCode(), "查询所有服务贸易行业配置信息失败：" + e.getMessage());
        }
    }
}
