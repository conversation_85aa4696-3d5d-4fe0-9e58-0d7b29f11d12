package com.ksher.productcontract.api.impl;

import com.alibaba.fastjson2.JSONObject;
import com.ksher.framework.model.Result;
import com.ksher.productcontract.api.dto.response.PlatformProductConfigDTO;
import com.ksher.productcontract.api.dto.response.SalesProductInfoDTO;
import com.ksher.productcontract.common.exception.ProductContractErrorEnum;
import com.ksher.productcontract.converter.PlatformProductConfigConverter;
import com.ksher.productcontract.converter.SalesProductInfoConverter;
import com.ksher.productcontract.coreservice.ProductCoreService;
import com.ksher.productcontract.model.PlatformProductConfig;
import com.ksher.productcontract.model.SalesProdKybInfo;
import com.ksher.productcontract.model.SalesProductInfo;
import com.ksher.productcontract.model.SolutionInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ProductFacadeImpl 单元测试类
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ProductFacadeImplTest {

    @Mock
    private ProductCoreService productCoreService;



    @InjectMocks
    private ProductFacadeImpl productFacadeImpl;

    private static final String VALID_SOLUTION_CODE = "SOL001";
    private static final String VALID_SALES_PRODUCT_CODE = "SP001";

    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }

    // ==================== getSolutionList 方法测试 ====================

    @Test
    void testGetSolutionList_Success() {
        // Given
        List<SolutionInfo> solutionInfoList = createMockSolutionInfoList();
        when(productCoreService.querySolutionList()).thenReturn(solutionInfoList);

        // When
        Result<List<SalesProductInfoDTO>> result = productFacadeImpl.getSolutionList();

        // Then
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(2, result.getData().size());

        // 验证转换逻辑
        SalesProductInfoDTO firstDto = result.getData().get(0);
        assertEquals("solution-1", firstDto.getSalesProductId());
        assertEquals("SOL001", firstDto.getSalesProductCode());
        assertEquals("解决方案1", firstDto.getSalesProductName());

        verify(productCoreService, times(1)).querySolutionList();
    }

    @Test
    void testGetSolutionList_EmptyList() {
        // Given
        when(productCoreService.querySolutionList()).thenReturn(new ArrayList<>());

        // When
        Result<List<SalesProductInfoDTO>> result = productFacadeImpl.getSolutionList();

        // Then
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertTrue(result.getData().isEmpty());
        verify(productCoreService, times(1)).querySolutionList();
    }

    @Test
    void testGetSolutionList_ServiceException() {
        // Given
        RuntimeException exception = new RuntimeException("数据库连接失败");
        when(productCoreService.querySolutionList()).thenThrow(exception);

        // When
        Result<List<SalesProductInfoDTO>> result = productFacadeImpl.getSolutionList();

        // Then
        assertFalse(result.isSuccess());
        assertEquals(ProductContractErrorEnum.GET_SOLUTION_ERROR.getCode(), result.getErrorCode());
        assertTrue(result.getErrorMsg().contains("查询解决方案列表失败"));
        assertTrue(result.getErrorMsg().contains("数据库连接失败"));
        verify(productCoreService, times(1)).querySolutionList();
    }

    // ==================== getSalesProductList 方法测试 ====================

    @Test
    void testGetSalesProductList_Success() {
        // Given
        List<SalesProductInfo> salesProductInfoList = createMockSalesProductInfoList();

        when(productCoreService.querySalesProductList(VALID_SOLUTION_CODE)).thenReturn(salesProductInfoList);

        // When
        Result<List<SalesProductInfoDTO>> result = productFacadeImpl.getSalesProductList(VALID_SOLUTION_CODE);

        // Then
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(2, result.getData().size());

        // 验证新增的属性
        SalesProductInfoDTO firstDto = result.getData().get(0);
        assertNotNull(firstDto.getSalesProductCode());
        assertNotNull(firstDto.getSalesProductName());

        verify(productCoreService, times(1)).querySalesProductList(VALID_SOLUTION_CODE);
    }

    @Test
    void testGetSalesProductList_EmptySolutionCode() {
        // When
        Result<List<SalesProductInfoDTO>> result = productFacadeImpl.getSalesProductList("");

        // Then
        assertFalse(result.isSuccess());
        assertEquals(ProductContractErrorEnum.GET_SALES_PRODUCT_ERROR.getCode(), result.getErrorCode());
        assertTrue(result.getErrorMsg().contains("查询销售产品列表失败"));
        verify(productCoreService, never()).querySalesProductList(anyString());
    }

    @Test
    void testGetSalesProductList_NullSolutionCode() {
        // When
        Result<List<SalesProductInfoDTO>> result = productFacadeImpl.getSalesProductList(null);

        // Then
        assertFalse(result.isSuccess());
        assertEquals(ProductContractErrorEnum.GET_SALES_PRODUCT_ERROR.getCode(), result.getErrorCode());
        assertTrue(result.getErrorMsg().contains("查询销售产品列表失败"));
        verify(productCoreService, never()).querySalesProductList(anyString());
    }

    @Test
    void testGetSalesProductList_ServiceException() {
        // Given
        RuntimeException exception = new RuntimeException("查询失败");
        when(productCoreService.querySalesProductList(VALID_SOLUTION_CODE)).thenThrow(exception);

        // When
        Result<List<SalesProductInfoDTO>> result = productFacadeImpl.getSalesProductList(VALID_SOLUTION_CODE);

        // Then
        assertFalse(result.isSuccess());
        assertEquals(ProductContractErrorEnum.GET_SALES_PRODUCT_ERROR.getCode(), result.getErrorCode());
        assertTrue(result.getErrorMsg().contains("查询销售产品列表失败"));
        verify(productCoreService, times(1)).querySalesProductList(VALID_SOLUTION_CODE);
    }

    // ==================== getKYBMaterialList 方法测试 ====================

    @Test
    void testGetKYBMaterialList_Success() {
        // Given
        List<SalesProdKybInfo> kybInfoList = createMockKybInfoList();
        when(productCoreService.queryKybMaterialList(VALID_SALES_PRODUCT_CODE)).thenReturn(kybInfoList);

        // When
        Result<List<Map<String, Object>>> result = productFacadeImpl.getKYBMaterialList(VALID_SALES_PRODUCT_CODE);

        // Then
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(2, result.getData().size());

        Map<String, Object> firstMap = result.getData().get(0);
        assertEquals("kyb-1", firstMap.get("pspkiId"));
        assertEquals("营业执照", firstMap.get("kybFieldName"));
        assertEquals("FILE", firstMap.get("kybFieldType"));
        assertEquals("营业执照扫描件", firstMap.get("kybFieldDesc"));
        assertEquals(true, firstMap.get("kybFieldRequired"));

        verify(productCoreService, times(1)).queryKybMaterialList(VALID_SALES_PRODUCT_CODE);
    }

    @Test
    void testGetKYBMaterialList_EmptySalesProductCode() {
        // When
        Result<List<Map<String, Object>>> result = productFacadeImpl.getKYBMaterialList("");

        // Then
        assertFalse(result.isSuccess());
        assertEquals(ProductContractErrorEnum.GET_SALES_PRODUCT_KYB_ERROR.getCode(), result.getErrorCode());
        assertTrue(result.getErrorMsg().contains("查询KYB材料列表失败"));
        verify(productCoreService, never()).queryKybMaterialList(anyString());
    }

    @Test
    void testGetKYBMaterialList_ServiceException() {
        // Given
        RuntimeException exception = new RuntimeException("KYB查询失败");
        when(productCoreService.queryKybMaterialList(VALID_SALES_PRODUCT_CODE)).thenThrow(exception);

        // When
        Result<List<Map<String, Object>>> result = productFacadeImpl.getKYBMaterialList(VALID_SALES_PRODUCT_CODE);

        // Then
        assertFalse(result.isSuccess());
        assertEquals(ProductContractErrorEnum.GET_SALES_PRODUCT_KYB_ERROR.getCode(), result.getErrorCode());
        assertTrue(result.getErrorMsg().contains("查询KYB材料列表失败"));
        verify(productCoreService, times(1)).queryKybMaterialList(VALID_SALES_PRODUCT_CODE);
    }

    // ==================== getAllPlatformProductConfigs 方法测试 ====================

    @Test
    void testGetAllPlatformProductConfigs_Success() {
        // Given
        List<PlatformProductConfig> platformProductConfigs = createMockPlatformProductConfigList();

        when(productCoreService.queryAllPlatformProductConfigs()).thenReturn(platformProductConfigs);

        // When
        Result<List<PlatformProductConfigDTO>> result = productFacadeImpl.getAllPlatformProductConfigs();

        // Then
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(2, result.getData().size());

        // 验证转换结果
        PlatformProductConfigDTO firstDto = result.getData().get(0);
        assertNotNull(firstDto.getPlatformProductCode());
        assertNotNull(firstDto.getSalesProductCode());

        verify(productCoreService, times(1)).queryAllPlatformProductConfigs();
    }

    @Test
    void testGetAllPlatformProductConfigs_EmptyList() {
        // Given
        when(productCoreService.queryAllPlatformProductConfigs()).thenReturn(new ArrayList<>());

        // When
        Result<List<PlatformProductConfigDTO>> result = productFacadeImpl.getAllPlatformProductConfigs();

        // Then
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertTrue(result.getData().isEmpty());
        verify(productCoreService, times(1)).queryAllPlatformProductConfigs();
    }

    @Test
    void testGetAllPlatformProductConfigs_ServiceException() {
        // Given
        RuntimeException exception = new RuntimeException("平台配置查询失败");
        when(productCoreService.queryAllPlatformProductConfigs()).thenThrow(exception);

        // When
        Result<List<PlatformProductConfigDTO>> result = productFacadeImpl.getAllPlatformProductConfigs();

        // Then
        assertFalse(result.isSuccess());
        assertEquals(ProductContractErrorEnum.GET_PLATFORM_PRODUCT_ERROR.getCode(), result.getErrorCode());
        assertTrue(result.getErrorMsg().contains("查询所有平台产品配置信息失败"));
        verify(productCoreService, times(1)).queryAllPlatformProductConfigs();
    }

    // ==================== 辅助方法 ====================

    private List<SolutionInfo> createMockSolutionInfoList() {
        List<SolutionInfo> list = new ArrayList<>();

        SolutionInfo solution1 = new SolutionInfo();
        solution1.setSolutionId("solution-1");
        solution1.setSolutionCode("SOL001");
        solution1.setSolutionName("解决方案1");
        solution1.setSolutionDescription("解决方案1描述");
        solution1.setStatus("ACTIVE");
        solution1.setCreateTime(LocalDateTime.now());
        solution1.setUpdateTime(LocalDateTime.now());
        solution1.setCreateUser("admin");
        solution1.setUpdateUser("admin");
        solution1.setApprover("manager");

        SolutionInfo solution2 = new SolutionInfo();
        solution2.setSolutionId("solution-2");
        solution2.setSolutionCode("SOL002");
        solution2.setSolutionName("解决方案2");
        solution2.setSolutionDescription("解决方案2描述");
        solution2.setStatus("ACTIVE");
        solution2.setCreateTime(LocalDateTime.now());
        solution2.setUpdateTime(LocalDateTime.now());
        solution2.setCreateUser("admin");
        solution2.setUpdateUser("admin");
        solution2.setApprover("manager");

        list.add(solution1);
        list.add(solution2);
        return list;
    }

    private List<SalesProductInfo> createMockSalesProductInfoList() {
        List<SalesProductInfo> list = new ArrayList<>();

        SalesProductInfo product1 = new SalesProductInfo();
        product1.setSalesProductId("sp-1");
        product1.setSalesProductCode("SP001");
        product1.setSalesProductName("销售产品1");
        product1.setSalesProductDescription("销售产品1描述");
        product1.setSolutionCode("SOL001");
        product1.setPlatformProductCodes(Arrays.asList("PP001", "PP002"));
        product1.setStatus("ACTIVE");
        product1.setCreateTime(LocalDateTime.now());
        product1.setUpdateTime(LocalDateTime.now());
        product1.setCreateUser("admin");
        product1.setUpdateUser("admin");
        product1.setApprover("manager");

        SalesProductInfo product2 = new SalesProductInfo();
        product2.setSalesProductId("sp-2");
        product2.setSalesProductCode("SP002");
        product2.setSalesProductName("销售产品2");
        product2.setSalesProductDescription("销售产品2描述");
        product2.setSolutionCode("SOL001");
        product2.setPlatformProductCodes(Arrays.asList("PP003", "PP004"));
        product2.setStatus("ACTIVE");
        product2.setCreateTime(LocalDateTime.now());
        product2.setUpdateTime(LocalDateTime.now());
        product2.setCreateUser("admin");
        product2.setUpdateUser("admin");
        product2.setApprover("manager");

        list.add(product1);
        list.add(product2);
        return list;
    }

    private List<SalesProductInfoDTO> createMockSalesProductInfoDTOList() {
        List<SalesProductInfoDTO> list = new ArrayList<>();

        SalesProductInfoDTO dto1 = new SalesProductInfoDTO();
        dto1.setSalesProductId("sp-1");
        dto1.setSalesProductCode("SP001");
        dto1.setSalesProductName("销售产品1");
        dto1.setSalesProductDescription("销售产品1描述");
        dto1.setSolutionCode("SOL001");
        dto1.setPlatformProductCodes(Arrays.asList("PP001", "PP002"));
        dto1.setStatus("ACTIVE");
        dto1.setCreateTime(LocalDateTime.now());
        dto1.setUpdateTime(LocalDateTime.now());
        dto1.setCreateUser("admin");
        dto1.setUpdateUser("admin");
        dto1.setApprover("manager");

        SalesProductInfoDTO dto2 = new SalesProductInfoDTO();
        dto2.setSalesProductId("sp-2");
        dto2.setSalesProductCode("SP002");
        dto2.setSalesProductName("销售产品2");
        dto2.setSalesProductDescription("销售产品2描述");
        dto2.setSolutionCode("SOL001");
        dto2.setPlatformProductCodes(Arrays.asList("PP003", "PP004"));
        dto2.setStatus("ACTIVE");
        dto2.setCreateTime(LocalDateTime.now());
        dto2.setUpdateTime(LocalDateTime.now());
        dto2.setCreateUser("admin");
        dto2.setUpdateUser("admin");
        dto2.setApprover("manager");

        list.add(dto1);
        list.add(dto2);
        return list;
    }

    private List<SalesProdKybInfo> createMockKybInfoList() {
        List<SalesProdKybInfo> list = new ArrayList<>();

        SalesProdKybInfo kyb1 = new SalesProdKybInfo();
        kyb1.setPspkiId("kyb-1");
        kyb1.setSalesProductCode(VALID_SALES_PRODUCT_CODE);
        JSONObject kybInfo1 = new JSONObject();
        kybInfo1.put("kybFieldName", "营业执照");
        kybInfo1.put("kybFieldType", "FILE");
        kybInfo1.put("kybFieldDesc", "营业执照扫描件");
        kybInfo1.put("kybFieldRequired", true);
        kyb1.setKybInfo(kybInfo1);
        kyb1.setCreateTime(LocalDateTime.now());
        kyb1.setUpdateTime(LocalDateTime.now());

        SalesProdKybInfo kyb2 = new SalesProdKybInfo();
        kyb2.setPspkiId("kyb-2");
        kyb2.setSalesProductCode(VALID_SALES_PRODUCT_CODE);
        JSONObject kybInfo2 = new JSONObject();
        kybInfo2.put("kybFieldType", "FILE");
        kybInfo2.put("kybFieldName", "身份证");
        kybInfo2.put("kybFieldDesc", "身份证扫描件");
        kybInfo2.put("kybFieldRequired", false);
        kyb2.setKybInfo(kybInfo2);
        kyb2.setCreateTime(LocalDateTime.now());
        kyb2.setUpdateTime(LocalDateTime.now());

        list.add(kyb1);
        list.add(kyb2);
        return list;
    }

    private List<PlatformProductConfig> createMockPlatformProductConfigList() {
        List<PlatformProductConfig> list = new ArrayList<>();

        PlatformProductConfig config1 = new PlatformProductConfig();
        config1.setPpConfigId("config-1");
        config1.setApprover("manager");
        config1.setCreateUser("admin");
        config1.setCreateTime(LocalDateTime.now());
        config1.setStatus("ACTIVE");
        config1.setCollectionCountry("中国");
        config1.setOriginalCurrency("RMB");
        config1.setReceiverBankCountry("中国");
        config1.setTargetCurrency("RMB");
        config1.setPlatformProductCode("PP001");
        config1.setSalesProductCode("SP001");
        ;
        config1.setUpdateTime(LocalDateTime.now());
        config1.setUpdateUser("admin");


        PlatformProductConfig config2 = new PlatformProductConfig();
        config2.setPpConfigId("config-2");
        config2.setApprover("manager");
        config2.setCreateUser("admin");
        config2.setCreateTime(LocalDateTime.now());
        config2.setStatus("ACTIVE");
        config2.setCollectionCountry("中国");
        config2.setOriginalCurrency("RMB");
        config2.setReceiverBankCountry("中国");
        config2.setTargetCurrency("RMB");
        config2.setPlatformProductCode("PP002");
        config2.setSalesProductCode("SP002");
        config2.setUpdateTime(LocalDateTime.now());
        config2.setUpdateUser("admin");

        list.add(config1);
        list.add(config2);
        return list;
    }

    private List<PlatformProductConfigDTO> createMockPlatformProductConfigDTOList() {
        List<PlatformProductConfigDTO> list = new ArrayList<>();

        PlatformProductConfigDTO dto1 = new PlatformProductConfigDTO();
        dto1.setPpConfigId("config-1");
        dto1.setApprover("manager");
        dto1.setCreateUser("admin");
        dto1.setCreateTime(LocalDateTime.now());
        dto1.setStatus("ACTIVE");
        dto1.setCollectionCountry("中国");
        dto1.setOriginalCurrency("RMB");
        dto1.setReceiverBankCountry("中国");
        dto1.setTargetCurrency("RMB");
        dto1.setPlatformProductCode("PP001");
        dto1.setSalesProductCode("SP001");

        PlatformProductConfigDTO dto2 = new PlatformProductConfigDTO();
        dto2.setPpConfigId("config-2");
        dto2.setApprover("manager");
        dto2.setCreateUser("admin");
        dto2.setCreateTime(LocalDateTime.now());
        dto2.setStatus("ACTIVE");
        dto2.setCollectionCountry("中国");
        dto2.setOriginalCurrency("RMB");
        dto2.setReceiverBankCountry("中国");
        dto2.setTargetCurrency("RMB");
        dto2.setPlatformProductCode("PP002");
        dto2.setSalesProductCode("SP002");
        dto2.setUpdateTime(LocalDateTime.now());
        dto2.setUpdateUser("admin");

        list.add(dto1);
        list.add(dto2);
        return list;
    }

    // ==================== getSalesProductListByMerchant 方法测试 ====================

    @Test
    void testGetSalesProductListByMerchant_Success() {
        // Given
        String merchantId = "MERCHANT001";
        List<SalesProductInfo> salesProductInfoList = createMockSalesProductInfoList();
        when(productCoreService.querySalesProductListByMerchant(merchantId)).thenReturn(salesProductInfoList);

        // When
        Result<List<SalesProductInfoDTO>> result = productFacadeImpl.getSalesProductListByMerchant(merchantId);

        // Then
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(2, result.getData().size());

        // 验证转换结果
        SalesProductInfoDTO firstDto = result.getData().get(0);
        assertEquals("SP001", firstDto.getSalesProductCode());
        assertEquals("销售产品1", firstDto.getSalesProductName());
        assertEquals("SOL001", firstDto.getSolutionCode());
        assertNotNull(firstDto.getPlatformProductCodes());

        verify(productCoreService, times(1)).querySalesProductListByMerchant(merchantId);
    }

    @Test
    void testGetSalesProductListByMerchant_EmptyMerchantId() {
        // When
        Result<List<SalesProductInfoDTO>> result = productFacadeImpl.getSalesProductListByMerchant("");

        // Then
        assertFalse(result.isSuccess());
        assertEquals(ProductContractErrorEnum.GET_SALES_PRODUCT_ERROR.getCode(), result.getErrorCode());
        assertTrue(result.getErrorMsg().contains("商户ID不能为空"));
        verify(productCoreService, never()).querySalesProductListByMerchant(anyString());
    }

    @Test
    void testGetSalesProductListByMerchant_ServiceException() {
        // Given
        String merchantId = "MERCHANT001";
        RuntimeException exception = new RuntimeException("查询失败");
        when(productCoreService.querySalesProductListByMerchant(merchantId)).thenThrow(exception);

        // When
        Result<List<SalesProductInfoDTO>> result = productFacadeImpl.getSalesProductListByMerchant(merchantId);

        // Then
        assertFalse(result.isSuccess());
        assertEquals(ProductContractErrorEnum.GET_SALES_PRODUCT_ERROR.getCode(), result.getErrorCode());
        assertTrue(result.getErrorMsg().contains("查询商户签约的销售产品列表失败"));
        verify(productCoreService, times(1)).querySalesProductListByMerchant(merchantId);
    }
}
