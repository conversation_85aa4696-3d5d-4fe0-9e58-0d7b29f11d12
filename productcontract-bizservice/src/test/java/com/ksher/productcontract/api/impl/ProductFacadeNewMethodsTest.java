package com.ksher.productcontract.api.impl;

import com.ksher.framework.model.Result;
import com.ksher.productcontract.api.dto.response.GoodsTradeCategoryDTO;
import com.ksher.productcontract.api.dto.response.ServiceTradeConfigDTO;
import com.ksher.productcontract.common.exception.ProductContractErrorEnum;
import com.ksher.productcontract.coreservice.ProductCoreService;
import com.ksher.productcontract.model.GoodsTradeCategory;
import com.ksher.productcontract.model.ServiceTradeConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * ProductFacadeImpl 新增方法测试类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/22
 */
@ExtendWith(MockitoExtension.class)
class ProductFacadeNewMethodsTest {

    @Mock
    private ProductCoreService productCoreService;

    @InjectMocks
    private ProductFacadeImpl productFacadeImpl;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }

    // ==================== getAllGoodsTradeCategories 方法测试 ====================

    @Test
    void testGetAllGoodsTradeCategories_Success() {
        // Given
        List<GoodsTradeCategory> goodsTradeCategories = createMockGoodsTradeCategoryList();
        when(productCoreService.queryAllGoodsTradeCategories()).thenReturn(goodsTradeCategories);

        // When
        Result<List<GoodsTradeCategoryDTO>> result = productFacadeImpl.getAllGoodsTradeCategories();

        // Then
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(2, result.getData().size());

        // 验证转换结果
        GoodsTradeCategoryDTO firstDto = result.getData().get(0);
        assertEquals("GTC001", firstDto.getCatagoryValue());
        assertEquals("货物贸易类目1", firstDto.getCatagoryName());
        assertEquals("Goods Trade Category 1", firstDto.getCatagoryNameEn());

        verify(productCoreService, times(1)).queryAllGoodsTradeCategories();
    }

    @Test
    void testGetAllGoodsTradeCategories_EmptyList() {
        // Given
        when(productCoreService.queryAllGoodsTradeCategories()).thenReturn(new ArrayList<>());

        // When
        Result<List<GoodsTradeCategoryDTO>> result = productFacadeImpl.getAllGoodsTradeCategories();

        // Then
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertTrue(result.getData().isEmpty());
        verify(productCoreService, times(1)).queryAllGoodsTradeCategories();
    }

    @Test
    void testGetAllGoodsTradeCategories_ServiceException() {
        // Given
        RuntimeException exception = new RuntimeException("数据库查询失败");
        when(productCoreService.queryAllGoodsTradeCategories()).thenThrow(exception);

        // When
        Result<List<GoodsTradeCategoryDTO>> result = productFacadeImpl.getAllGoodsTradeCategories();

        // Then
        assertFalse(result.isSuccess());
        assertEquals(ProductContractErrorEnum.GET_PLATFORM_PRODUCT_ERROR.getCode(), result.getErrorCode());
        assertTrue(result.getErrorMsg().contains("查询所有货物贸易销售类目信息失败"));
        verify(productCoreService, times(1)).queryAllGoodsTradeCategories();
    }

    // ==================== getAllServiceTradeConfigs 方法测试 ====================

    @Test
    void testGetAllServiceTradeConfigs_Success() {
        // Given
        List<ServiceTradeConfig> serviceTradeConfigs = createMockServiceTradeConfigList();
        when(productCoreService.queryAllServiceTradeConfigs()).thenReturn(serviceTradeConfigs);

        // When
        Result<List<ServiceTradeConfigDTO>> result = productFacadeImpl.getAllServiceTradeConfigs();

        // Then
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(2, result.getData().size());

        // 验证转换结果
        ServiceTradeConfigDTO firstDto = result.getData().get(0);
        assertEquals("STC001", firstDto.getServiceTradeValue());
        assertEquals("服务贸易配置1", firstDto.getServiceTradeName());
        assertEquals("Service Trade Config 1", firstDto.getServiceTradeNameEn());

        verify(productCoreService, times(1)).queryAllServiceTradeConfigs();
    }

    @Test
    void testGetAllServiceTradeConfigs_EmptyList() {
        // Given
        when(productCoreService.queryAllServiceTradeConfigs()).thenReturn(new ArrayList<>());

        // When
        Result<List<ServiceTradeConfigDTO>> result = productFacadeImpl.getAllServiceTradeConfigs();

        // Then
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertTrue(result.getData().isEmpty());
        verify(productCoreService, times(1)).queryAllServiceTradeConfigs();
    }

    @Test
    void testGetAllServiceTradeConfigs_ServiceException() {
        // Given
        RuntimeException exception = new RuntimeException("配置查询失败");
        when(productCoreService.queryAllServiceTradeConfigs()).thenThrow(exception);

        // When
        Result<List<ServiceTradeConfigDTO>> result = productFacadeImpl.getAllServiceTradeConfigs();

        // Then
        assertFalse(result.isSuccess());
        assertEquals(ProductContractErrorEnum.GET_PLATFORM_PRODUCT_ERROR.getCode(), result.getErrorCode());
        assertTrue(result.getErrorMsg().contains("查询所有服务贸易行业配置信息失败"));
        verify(productCoreService, times(1)).queryAllServiceTradeConfigs();
    }

    // ==================== 辅助方法 ====================

    private List<GoodsTradeCategory> createMockGoodsTradeCategoryList() {
        List<GoodsTradeCategory> list = new ArrayList<>();

        GoodsTradeCategory category1 = new GoodsTradeCategory();
        category1.setPgtCatagoryId("gtc-1");
        category1.setCatagoryValue("GTC001");
        category1.setCatagoryName("货物贸易类目1");
        category1.setCatagoryNameEn("Goods Trade Category 1");
        category1.setStatus("ACTIVE");
        category1.setCreateTime(LocalDateTime.now());
        category1.setUpdateTime(LocalDateTime.now());
        category1.setCreateUser("admin");
        category1.setUpdateUser("admin");
        category1.setApprover("manager");

        GoodsTradeCategory category2 = new GoodsTradeCategory();
        category2.setPgtCatagoryId("gtc-2");
        category2.setCatagoryValue("GTC002");
        category2.setCatagoryName("货物贸易类目2");
        category2.setCatagoryNameEn("Goods Trade Category 2");
        category2.setStatus("ACTIVE");
        category2.setCreateTime(LocalDateTime.now());
        category2.setUpdateTime(LocalDateTime.now());
        category2.setCreateUser("admin");
        category2.setUpdateUser("admin");
        category2.setApprover("manager");

        list.add(category1);
        list.add(category2);
        return list;
    }

    private List<ServiceTradeConfig> createMockServiceTradeConfigList() {
        List<ServiceTradeConfig> list = new ArrayList<>();

        ServiceTradeConfig config1 = new ServiceTradeConfig();
        config1.setPstConfigId("stc-1");
        config1.setServiceTradeValue("STC001");
        config1.setServiceTradeName("服务贸易配置1");
        config1.setServiceTradeNameEn("Service Trade Config 1");
        config1.setCurrenciesSupport("USD,EUR,CNY");
        config1.setDescription("服务贸易配置1描述");
        config1.setFundInwardSupport("Y");
        config1.setLocalPaymentSupport("Y");
        config1.setGlobalBusinessSupport("Y");
        config1.setStatus("ACTIVE");
        config1.setCreateTime(LocalDateTime.now());
        config1.setUpdateTime(LocalDateTime.now());
        config1.setCreateUser("admin");
        config1.setUpdateUser("admin");
        config1.setApprover("manager");

        ServiceTradeConfig config2 = new ServiceTradeConfig();
        config2.setPstConfigId("stc-2");
        config2.setServiceTradeValue("STC002");
        config2.setServiceTradeName("服务贸易配置2");
        config2.setServiceTradeNameEn("Service Trade Config 2");
        config2.setCurrenciesSupport("USD,CNY");
        config2.setDescription("服务贸易配置2描述");
        config2.setFundInwardSupport("N");
        config2.setLocalPaymentSupport("Y");
        config2.setGlobalBusinessSupport("N");
        config2.setStatus("ACTIVE");
        config2.setCreateTime(LocalDateTime.now());
        config2.setUpdateTime(LocalDateTime.now());
        config2.setCreateUser("admin");
        config2.setUpdateUser("admin");
        config2.setApprover("manager");

        list.add(config1);
        list.add(config2);
        return list;
    }
}
