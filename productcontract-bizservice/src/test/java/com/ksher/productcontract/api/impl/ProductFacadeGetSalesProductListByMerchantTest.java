package com.ksher.productcontract.api.impl;

import com.ksher.framework.model.Result;
import com.ksher.productcontract.api.dto.response.SalesProductInfoDTO;
import com.ksher.productcontract.common.exception.ProductContractErrorEnum;
import com.ksher.productcontract.coreservice.ProductCoreService;
import com.ksher.productcontract.model.SalesProductInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ProductFacadeImpl getSalesProductListByMerchant方法测试类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/22
 */
@ExtendWith(MockitoExtension.class)
class ProductFacadeGetSalesProductListByMerchantTest {

    @Mock
    private ProductCoreService productCoreService;

    @InjectMocks
    private ProductFacadeImpl productFacadeImpl;

    private static final String VALID_MERCHANT_ID = "MERCHANT001";
    private static final String INVALID_MERCHANT_ID = "";

    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }

    @Test
    void testGetSalesProductListByMerchant_Success() {
        // Given
        List<SalesProductInfo> salesProductInfoList = createMockSalesProductInfoList();
        when(productCoreService.querySalesProductListByMerchant(VALID_MERCHANT_ID)).thenReturn(salesProductInfoList);

        // When
        Result<List<SalesProductInfoDTO>> result = productFacadeImpl.getSalesProductListByMerchant(VALID_MERCHANT_ID);

        // Then
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(2, result.getData().size());

        // 验证第一个销售产品信息
        SalesProductInfoDTO firstDto = result.getData().get(0);
        assertEquals("SP001", firstDto.getSalesProductCode());
        assertEquals("销售产品1", firstDto.getSalesProductName());
        assertEquals("SOL001", firstDto.getSolutionCode());
        assertNotNull(firstDto.getPlatformProductCodes());
        assertEquals(2, firstDto.getPlatformProductCodes().size());
        assertTrue(firstDto.getPlatformProductCodes().contains("PP001"));
        assertTrue(firstDto.getPlatformProductCodes().contains("PP002"));

        verify(productCoreService, times(1)).querySalesProductListByMerchant(VALID_MERCHANT_ID);
    }

    @Test
    void testGetSalesProductListByMerchant_EmptyMerchantId() {
        // When
        Result<List<SalesProductInfoDTO>> result = productFacadeImpl.getSalesProductListByMerchant(INVALID_MERCHANT_ID);

        // Then
        assertFalse(result.isSuccess());
        assertEquals(ProductContractErrorEnum.GET_SALES_PRODUCT_ERROR.getCode(), result.getErrorCode());
        assertTrue(result.getErrorMsg().contains("商户ID不能为空"));
        verify(productCoreService, never()).querySalesProductListByMerchant(anyString());
    }

    @Test
    void testGetSalesProductListByMerchant_NullMerchantId() {
        // When
        Result<List<SalesProductInfoDTO>> result = productFacadeImpl.getSalesProductListByMerchant(null);

        // Then
        assertFalse(result.isSuccess());
        assertEquals(ProductContractErrorEnum.GET_SALES_PRODUCT_ERROR.getCode(), result.getErrorCode());
        assertTrue(result.getErrorMsg().contains("商户ID不能为空"));
        verify(productCoreService, never()).querySalesProductListByMerchant(anyString());
    }

    @Test
    void testGetSalesProductListByMerchant_EmptyList() {
        // Given
        when(productCoreService.querySalesProductListByMerchant(VALID_MERCHANT_ID)).thenReturn(new ArrayList<>());

        // When
        Result<List<SalesProductInfoDTO>> result = productFacadeImpl.getSalesProductListByMerchant(VALID_MERCHANT_ID);

        // Then
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertTrue(result.getData().isEmpty());
        verify(productCoreService, times(1)).querySalesProductListByMerchant(VALID_MERCHANT_ID);
    }

    @Test
    void testGetSalesProductListByMerchant_ServiceException() {
        // Given
        RuntimeException exception = new RuntimeException("数据库查询失败");
        when(productCoreService.querySalesProductListByMerchant(VALID_MERCHANT_ID)).thenThrow(exception);

        // When
        Result<List<SalesProductInfoDTO>> result = productFacadeImpl.getSalesProductListByMerchant(VALID_MERCHANT_ID);

        // Then
        assertFalse(result.isSuccess());
        assertEquals(ProductContractErrorEnum.GET_SALES_PRODUCT_ERROR.getCode(), result.getErrorCode());
        assertTrue(result.getErrorMsg().contains("查询商户签约的销售产品列表失败"));
        assertTrue(result.getErrorMsg().contains("数据库查询失败"));
        verify(productCoreService, times(1)).querySalesProductListByMerchant(VALID_MERCHANT_ID);
    }

    // ==================== 辅助方法 ====================

    private List<SalesProductInfo> createMockSalesProductInfoList() {
        List<SalesProductInfo> list = new ArrayList<>();

        SalesProductInfo product1 = new SalesProductInfo();
        product1.setSalesProductId("sp-1");
        product1.setSalesProductCode("SP001");
        product1.setSalesProductName("销售产品1");
        product1.setSalesProductDescription("销售产品1描述");
        product1.setSolutionCode("SOL001");
        product1.setPlatformProductCodes(Arrays.asList("PP001", "PP002"));
        product1.setStatus("ACTIVE");
        product1.setCreateTime(LocalDateTime.now());
        product1.setUpdateTime(LocalDateTime.now());
        product1.setCreateUser("admin");
        product1.setUpdateUser("admin");
        product1.setApprover("manager");

        SalesProductInfo product2 = new SalesProductInfo();
        product2.setSalesProductId("sp-2");
        product2.setSalesProductCode("SP002");
        product2.setSalesProductName("销售产品2");
        product2.setSalesProductDescription("销售产品2描述");
        product2.setSolutionCode("SOL002");
        product2.setPlatformProductCodes(Arrays.asList("PP003", "PP004"));
        product2.setStatus("ACTIVE");
        product2.setCreateTime(LocalDateTime.now());
        product2.setUpdateTime(LocalDateTime.now());
        product2.setCreateUser("admin");
        product2.setUpdateUser("admin");
        product2.setApprover("manager");

        list.add(product1);
        list.add(product2);
        return list;
    }
}
